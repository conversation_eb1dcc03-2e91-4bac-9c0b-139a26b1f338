package com.mingdao.edge.core.mqtt.utils;

import com.mingdao.edge.core.common.context.SpringContextUtils;
import com.mingdao.edge.core.mqtt.config.MqttClientInfo;
import com.mingdao.edge.core.mqtt.handler.DefaultMqttCallback;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.mqttv5.client.IMqttMessageListener;
import org.eclipse.paho.mqttv5.common.MqttException;
import org.eclipse.paho.mqttv5.common.MqttSubscription;

import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Slf4j
public class RegisterMqttClientUtil {

    public static void registerMqttClient(String broker, String clientId, String userName, String password, List<String> topics, IMqttMessageListener messageListener) throws MqttException {
        MqttClientInfo mqttClientInfo = new MqttClientInfo();
        mqttClientInfo.setBroker(broker);
        mqttClientInfo.setClientId(clientId);
        mqttClientInfo.setUserName(userName);
        mqttClientInfo.setPassword(password);

        // 处理订阅主题
        if (topics != null && !topics.isEmpty()) {
            MqttSubscription[] subscriptions = topics.stream()
                    .map(topic -> new MqttSubscription(topic, 2))
                    .toArray(MqttSubscription[]::new);
            mqttClientInfo.setSubscriptions(subscriptions);

            // 为每个主题创建相同的消息监听器
            IMqttMessageListener[] listeners = new IMqttMessageListener[topics.size()];
            for (int i = 0; i < topics.size(); i++) {
                listeners[i] = messageListener;
            }
            mqttClientInfo.setMessageListeners(listeners);
        }

        log.info("context: {}", SpringContextUtils.getApplicationContext().getId());


        mqttClientInfo.setCallbackHandler(new DefaultMqttCallback(mqttClientInfo));

        SpringContextUtils.registerBean("mqttClientInfo", mqttClientInfo);

        mqttClientInfo.createMqttClient();
    }
}
