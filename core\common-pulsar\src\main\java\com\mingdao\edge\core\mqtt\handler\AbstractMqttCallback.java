package com.mingdao.edge.core.mqtt.handler;

import com.mingdao.edge.core.mqtt.config.MqttClientInfo;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.mqttv5.client.IMqttDeliveryToken;
import org.eclipse.paho.mqttv5.client.IMqttToken;
import org.eclipse.paho.mqttv5.client.MqttCallback;
import org.eclipse.paho.mqttv5.client.MqttDisconnectResponse;
import org.eclipse.paho.mqttv5.common.MqttException;
import org.eclipse.paho.mqttv5.common.MqttMessage;
import org.eclipse.paho.mqttv5.common.packet.MqttProperties;

/**
 * MQTT回调抽象基类
 * 提供 MqttCallback 接口的默认实现，子类可以根据需要重写相应方法
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Slf4j
public abstract class AbstractMqttCallback implements MqttCallback {

    protected MqttClientInfo mqttClientInfo;

    public AbstractMqttCallback(MqttClientInfo mqttClientInfo) {
        this.mqttClientInfo = mqttClientInfo;
    }

    /**
     * 连接丢失时的回调
     * 当与服务器的连接丢失时调用此方法
     *
     * @param cause 连接丢失的原因
     */
    public void connectionLost(Throwable cause) {
        log.warn("MQTT连接丢失: {}", cause.getMessage());
        onConnectionLost(cause);
    }

    /**
     * 消息到达时的回调
     * 当收到订阅主题的消息时调用此方法
     *
     * @param topic 消息主题
     * @param message 消息内容
     * @throws Exception 处理消息时可能抛出的异常
     */
    public void messageArrived(String topic, MqttMessage message) throws Exception {
        log.debug("收到MQTT消息 - 主题: {}, QoS: {}", topic, message.getQos());
        onMessageArrived(topic, message);
    }

    /**
     * 消息发送完成时的回调 (IMqttToken版本)
     * 当消息发送完成时调用此方法
     *
     * @param token 发送令牌
     */
    public void deliveryComplete(IMqttToken token) {
        log.debug("MQTT消息发送完成 (IMqttToken): {}", token.isComplete());
        onDeliveryComplete(token);
    }

    /**
     * 消息发送完成时的回调 (IMqttDeliveryToken版本)
     * 当消息发送完成时调用此方法
     *
     * @param token 发送令牌
     */
    public void deliveryComplete(IMqttDeliveryToken token) {
        log.debug("MQTT消息发送完成 (IMqttDeliveryToken): {}", token.isComplete());
        onDeliveryComplete(token);
    }

    /**
     * 连接完成时的回调
     * 当与服务器的连接建立完成时调用此方法
     *
     * @param reconnect 是否为重连
     * @param serverURI 服务器URI
     */
    public void connectComplete(boolean reconnect, String serverURI) {
        log.info("MQTT连接完成 - 重连: {}, 服务器: {}", reconnect, serverURI);
        onConnectComplete(reconnect, serverURI);
    }

    /**
     * 认证包到达时的回调
     * 当收到认证包时调用此方法
     *
     * @param reasonCode 原因代码
     * @param properties 属性
     */
    public void authPacketArrived(int reasonCode, MqttProperties properties) {
        log.debug("MQTT认证包到达 - 原因代码: {}", reasonCode);
        onAuthPacketArrived(reasonCode, properties);
    }

    /**
     * 断开连接时的回调
     * 当与服务器断开连接时调用此方法
     *
     * @param disconnectResponse 断开连接响应
     */
    public void disconnected(MqttDisconnectResponse disconnectResponse) {
        log.info("MQTT连接断开 - 原因代码: {}", disconnectResponse.getReturnCode());
        onDisconnected(disconnectResponse);
    }

    /**
     * MQTT错误发生时的回调
     * 当MQTT客户端发生错误时调用此方法
     *
     * @param exception MQTT异常
     */
    public void mqttErrorOccurred(MqttException exception) {
        log.error("MQTT错误发生: {}", exception.getMessage(), exception);
        onMqttErrorOccurred(exception);
    }

    // ==================== 子类可重写的抽象方法 ====================

    /**
     * 连接丢失处理
     * 子类可以重写此方法来实现自定义的连接丢失处理逻辑
     *
     * @param cause 连接丢失的原因
     */
    protected void onConnectionLost(Throwable cause) {
        // 默认实现为空，子类可以重写
    }

    /**
     * 消息到达处理
     * 子类可以重写此方法来实现自定义的消息处理逻辑
     *
     * @param topic 消息主题
     * @param message 消息内容
     * @throws Exception 处理消息时可能抛出的异常
     */
    protected void onMessageArrived(String topic, MqttMessage message) throws Exception {
        // 默认实现为空，子类可以重写
    }

    /**
     * 消息发送完成处理 (IMqttToken版本)
     * 子类可以重写此方法来实现自定义的发送完成处理逻辑
     *
     * @param token 发送令牌
     */
    protected void onDeliveryComplete(IMqttToken token) {
        // 默认实现为空，子类可以重写
    }

    /**
     * 消息发送完成处理 (IMqttDeliveryToken版本)
     * 子类可以重写此方法来实现自定义的发送完成处理逻辑
     *
     * @param token 发送令牌
     */
    protected void onDeliveryComplete(IMqttDeliveryToken token) {
        // 默认实现为空，子类可以重写
    }

    /**
     * 连接完成处理
     * 子类可以重写此方法来实现自定义的连接完成处理逻辑
     *
     * @param reconnect 是否为重连
     * @param serverURI 服务器URI
     */
    protected void onConnectComplete(boolean reconnect, String serverURI) {
        // 默认实现为空，子类可以重写
    }

    /**
     * 认证包到达处理
     * 子类可以重写此方法来实现自定义的认证包处理逻辑
     *
     * @param reasonCode 原因代码
     * @param properties 属性
     */
    protected void onAuthPacketArrived(int reasonCode, MqttProperties properties) {
        // 默认实现为空，子类可以重写
    }

    /**
     * 断开连接处理
     * 子类可以重写此方法来实现自定义的断开连接处理逻辑
     *
     * @param disconnectResponse 断开连接响应
     */
    protected void onDisconnected(MqttDisconnectResponse disconnectResponse) {
        // 默认实现为空，子类可以重写
        // 日志记录断开原因
        System.out.println("MQTT连接丢失，原因：" + disconnectResponse.getReasonString());
        // 自动重连
        while (true) {
            try {
                Thread.sleep(5000); // 5秒后重试
                mqttClientInfo.createMqttClient();
                System.out.println("重连成功！");
                break;
            } catch (Exception e) {
                System.out.println("重连失败，5秒后重试...");
            }
        }
    }

    /**
     * MQTT错误发生处理
     * 子类可以重写此方法来实现自定义的MQTT错误处理逻辑
     *
     * @param exception MQTT异常
     */
    protected void onMqttErrorOccurred(MqttException exception) {
        // 默认实现为空，子类可以重写
    }
}
