package com.mingdao.edge.core.mqtt.service;

import com.alibaba.fastjson.JSONObject;
import com.mingdao.edge.core.common.context.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.mqttv5.client.MqttClient;
import org.eclipse.paho.mqttv5.common.MqttMessage;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

/**
 * MQTT消息发布服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MqttService {

    /**
     * 发布消息
     * @param topic 主题
     * @param payload 消息内容
     * @param qos QoS等级
     * @param retained 是否保留
     */
    public <T> void publish(String topic, T payload, int qos, boolean retained) {
        try {
            byte[] payloadBytes;
            if (payload instanceof String) {
                payloadBytes = ((String) payload).getBytes(StandardCharsets.UTF_8);
            } else {
                payloadBytes = JSONObject.toJSONString(payload).getBytes(StandardCharsets.UTF_8);
            }
            MqttMessage message = new MqttMessage(payloadBytes);
            message.setQos(qos);
            message.setRetained(retained);
            //log.info("publish context: {}", SpringContextUtils.getApplicationContext().getId());
            MqttClient mqttClient = SpringContextUtils.getBean(MqttClient.class);
            if (mqttClient.isConnected()) {
                mqttClient.publish(topic, message);
                log.debug("MQTT消息已发布，topic={}, payloadBytes.length={}", topic, payloadBytes.length);
            }
        } catch (Exception e) {
            log.error("MQTT消息发布失败:" + e.getMessage(), e);
        }
    }

    /**
     * 发布消息，默认qos=1，retained=false
     * @param topic 主题
     * @param payload 消息内容
     */
    public <T> void publish(String topic, T payload) {
        publish(topic, payload, 2, false);
    }

    /**
     * 发布消息，默认qos=1，retained=false
     * @param topic 主题
     * @param payload 消息内容
     */
    public void publishRetained(String topic, JSONObject payload) {
        publish(topic, payload, 2, true);
    }
}
